# Configure Terraform to set the required AzureRM provider
# version and features{} block

# terraform {
#   required_providers {
#     azurerm = {
#       source  = "hashicorp/azurerm"
#       version = "~> 3.107"
#     }
#   }
# }

# Define the provider configuration

provider "azurerm" {
  features {}

  subscription_id = var.subscription_id_management
}

# Get the current client configuration from the AzureRM provider

data "azurerm_client_config" "current" {}

# Local values for management configuration
locals {
  # Common tags for Log Analytics Workspaces
  infrastructure_law_tags = merge(var.tags, {
    Purpose     = "Infrastructure Monitoring"
    WorkspaceType = "Infrastructure"
    DataSources = "Infrastructure,Platform,VMs"
  })

  security_law_tags = merge(var.tags, {
    Purpose     = "Security Monitoring"
    WorkspaceType = "Security"
    DataSources = "Security,Identity,Audit,Sentinel"
  })
}

# Declare the Azure landing zones Terraform module
# and provide the connectivity configuration.

# Create management resource group with custom naming convention
resource "azurerm_resource_group" "management" {
  name     = "${var.root_id}-rg-management-${var.primary_location}"
  location = var.primary_location
  tags     = var.tags
}

# ==============================================================================
# LOG ANALYTICS WORKSPACE FOR INFRASTRUCTURE
# ==============================================================================
# Thu thập log và metrics từ các tài nguyên hạ tầng dùng chung và nền tảng
# (Azure VNets, Gateways, Azure Firewall, Key Vaults, VMs quản lý, v.v.)

resource "azurerm_log_analytics_workspace" "infrastructure" {
  name                = "${var.root_id}-law-infrastructure-${var.primary_location}"
  location            = var.primary_location
  resource_group_name = azurerm_resource_group.management.name
  sku                 = var.law_sku
  retention_in_days   = var.log_retention_in_days
  daily_quota_gb      = var.law_daily_quota_gb

  tags = local.infrastructure_law_tags
}

# Infrastructure monitoring solutions
resource "azurerm_log_analytics_solution" "vm_insights_infra" {
  solution_name         = "VMInsights"
  location              = var.primary_location
  resource_group_name   = azurerm_resource_group.management.name
  workspace_resource_id = azurerm_log_analytics_workspace.infrastructure.id
  workspace_name        = azurerm_log_analytics_workspace.infrastructure.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/VMInsights"
  }

  tags = local.infrastructure_law_tags
}

resource "azurerm_log_analytics_solution" "change_tracking_infra" {
  solution_name         = "ChangeTracking"
  location              = var.primary_location
  resource_group_name   = azurerm_resource_group.management.name
  workspace_resource_id = azurerm_log_analytics_workspace.infrastructure.id
  workspace_name        = azurerm_log_analytics_workspace.infrastructure.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/ChangeTracking"
  }

  tags = local.infrastructure_law_tags
}

resource "azurerm_log_analytics_solution" "updates_infra" {
  solution_name         = "Updates"
  location              = var.primary_location
  resource_group_name   = azurerm_resource_group.management.name
  workspace_resource_id = azurerm_log_analytics_workspace.infrastructure.id
  workspace_name        = azurerm_log_analytics_workspace.infrastructure.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/Updates"
  }

  tags = local.infrastructure_law_tags
}

resource "azurerm_log_analytics_solution" "agent_health_infra" {
  solution_name         = "AgentHealthAssessment"
  location              = var.primary_location
  resource_group_name   = azurerm_resource_group.management.name
  workspace_resource_id = azurerm_log_analytics_workspace.infrastructure.id
  workspace_name        = azurerm_log_analytics_workspace.infrastructure.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/AgentHealthAssessment"
  }

  tags = local.infrastructure_law_tags
}

# ==============================================================================
# LOG ANALYTICS WORKSPACE FOR SECURITY
# ==============================================================================
# Thu thập log bảo mật từ các dịch vụ Identity, mạng, bảo mật và kiểm toán
# Workspace này được thiết kế làm nguồn dữ liệu chính cho Azure Sentinel

resource "azurerm_log_analytics_workspace" "security" {
  name                = "${var.root_id}-law-security-${var.primary_location}"
  location            = var.primary_location
  resource_group_name = azurerm_resource_group.management.name
  sku                 = "PerGB2018"
  retention_in_days   = var.security_log_retention_in_days
  daily_quota_gb      = var.security_law_daily_quota_gb

  tags = local.security_law_tags
}

# Microsoft Sentinel - Enable on Security LAW
resource "azurerm_sentinel_log_analytics_workspace_onboarding" "security" {
  count                         = var.enable_sentinel ? 1 : 0
  workspace_id                  = azurerm_log_analytics_workspace.security.id
  customer_managed_key_enabled  = false
}

# Security-focused solutions
resource "azurerm_log_analytics_solution" "security" {
  solution_name         = "Security"
  location              = var.primary_location
  resource_group_name   = azurerm_resource_group.management.name
  workspace_resource_id = azurerm_log_analytics_workspace.security.id
  workspace_name        = azurerm_log_analytics_workspace.security.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/Security"
  }

  tags = local.security_law_tags
}

resource "azurerm_log_analytics_solution" "security_insights" {
  count                 = var.enable_sentinel ? 1 : 0
  solution_name         = "SecurityInsights"
  location              = var.primary_location
  resource_group_name   = azurerm_resource_group.management.name
  workspace_resource_id = azurerm_log_analytics_workspace.security.id
  workspace_name        = azurerm_log_analytics_workspace.security.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/SecurityInsights"
  }

  tags = local.security_law_tags
}

# ==============================================================================
# DATA COLLECTION RULES FOR INFRASTRUCTURE LAW
# ==============================================================================

# DCR for VM Insights (Infrastructure LAW)
resource "azurerm_monitor_data_collection_rule" "vm_insights_infra" {
  count               = var.enable_vminsights_dcr ? 1 : 0
  name                = "${var.root_id}-dcr-vminsights-infra-${var.primary_location}"
  resource_group_name = azurerm_resource_group.management.name
  location            = var.primary_location

  destinations {
    log_analytics {
      workspace_resource_id = azurerm_log_analytics_workspace.infrastructure.id
      name                  = "VMInsightsPerf-Logs-Dest"
    }
  }

  data_flow {
    streams      = ["Microsoft-InsightsMetrics", "Microsoft-Perf"]
    destinations = ["VMInsightsPerf-Logs-Dest"]
  }

  data_sources {
    performance_counter {
      streams                       = ["Microsoft-Perf", "Microsoft-InsightsMetrics"]
      sampling_frequency_in_seconds = 60
      counter_specifiers = [
        "\\Processor Information(_Total)\\% Processor Time",
        "\\Processor Information(_Total)\\% Privileged Time",
        "\\Processor Information(_Total)\\% User Time",
        "\\Processor Information(_Total)\\Processor Frequency",
        "\\System\\Processes",
        "\\Process(_Total)\\Thread Count",
        "\\Process(_Total)\\Handle Count",
        "\\System\\System Up Time",
        "\\System\\Context Switches/sec",
        "\\System\\Processor Queue Length",
        "\\Memory\\% Committed Bytes In Use",
        "\\Memory\\Available Bytes",
        "\\Memory\\Committed Bytes",
        "\\Memory\\Cache Bytes",
        "\\Memory\\Pool Paged Bytes",
        "\\Memory\\Pool Nonpaged Bytes",
        "\\Memory\\Pages/sec",
        "\\Memory\\Page Faults/sec",
        "\\Process(_Total)\\Working Set",
        "\\Process(_Total)\\Working Set - Private",
        "\\LogicalDisk(_Total)\\% Disk Time",
        "\\LogicalDisk(_Total)\\% Disk Read Time",
        "\\LogicalDisk(_Total)\\% Disk Write Time",
        "\\LogicalDisk(_Total)\\% Idle Time",
        "\\LogicalDisk(_Total)\\Disk Bytes/sec",
        "\\LogicalDisk(_Total)\\Disk Read Bytes/sec",
        "\\LogicalDisk(_Total)\\Disk Write Bytes/sec",
        "\\LogicalDisk(_Total)\\Disk Transfers/sec",
        "\\LogicalDisk(_Total)\\Disk Reads/sec",
        "\\LogicalDisk(_Total)\\Disk Writes/sec",
        "\\LogicalDisk(_Total)\\Avg. Disk sec/Transfer",
        "\\LogicalDisk(_Total)\\Avg. Disk sec/Read",
        "\\LogicalDisk(_Total)\\Avg. Disk sec/Write",
        "\\LogicalDisk(_Total)\\Avg. Disk Queue Length",
        "\\LogicalDisk(_Total)\\Avg. Disk Read Queue Length",
        "\\LogicalDisk(_Total)\\Avg. Disk Write Queue Length",
        "\\LogicalDisk(_Total)\\% Free Space",
        "\\LogicalDisk(_Total)\\Free Megabytes",
        "\\Network Interface(*)\\Bytes Total/sec",
        "\\Network Interface(*)\\Bytes Sent/sec",
        "\\Network Interface(*)\\Bytes Received/sec",
        "\\Network Interface(*)\\Packets/sec",
        "\\Network Interface(*)\\Packets Sent/sec",
        "\\Network Interface(*)\\Packets Received/sec",
        "\\Network Interface(*)\\Packets Outbound Errors",
        "\\Network Interface(*)\\Packets Received Errors"
      ]
      name = "perfCounterDataSource60"
    }
  }

  tags = local.infrastructure_law_tags
}
