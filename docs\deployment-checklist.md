# Azure Landing Zone - Deployment Checklist

## Thứ Tự Triển Khai Resources

| STT | Công việc thực hiện | <PERSON><PERSON><PERSON> bước kiểm tra/ kiểm thử | Terraform module path | Trạng thái công việc | Note |
|-----|-------------------|---------------------------|---------------------|-------------------|------|
| **PHASE 1: CORE FOUNDATION** |
| 1 | Tạo Azure AD Groups | - Kiểm tra groups tồn tại trong Azure AD<br>- Verify members và permissions | `./core/modules/azure_ad_groups` | ⏳ Pending | Optional - chỉ khi có cấu hình |
| 2 | Tạo Root Management Group | - Kiểm tra MG hierarchy trong Azure Portal<br>- Verify naming convention | `./core` | ⏳ Pending | Foundation cho toàn bộ Landing Zone |
| 3 | Tạo Platform Management Group | - Kiểm tra parent-child relationship<br>- Verify subscription assignments | `./core` | ⏳ Pending | Container cho platform services |
| 4 | Tạo Landing Zones Management Group | - Kiểm tra MG structure<br>- Verify policy inheritance | `./core` | ⏳ Pending | Container cho workload subscriptions |
| 5 | Tạo Identity Management Group | - Kiểm tra MG tồn tại<br>- Verify subscription mapping | `./core` | ⏳ Pending | Cho identity services |
| 6 | Tạo Management Management Group | - Kiểm tra MG structure<br>- Verify policy assignments | `./core` | ⏳ Pending | Cho monitoring và governance |
| 7 | Tạo Connectivity Management Group | - Kiểm tra MG hierarchy<br>- Verify subscription assignments | `./core` | ⏳ Pending | Cho networking resources |
| 8 | Tạo Productions Management Group | - Kiểm tra parent relationship với Landing Zones<br>- Verify policy inheritance | `./core` | ⏳ Pending | Container cho production workloads |
| 9 | Tạo Non-Productions Management Group | - Kiểm tra MG structure<br>- Verify policy assignments | `./core` | ⏳ Pending | Container cho non-prod workloads |
| 10 | Tạo DevTest Management Group | - Kiểm tra parent relationship<br>- Verify subscription assignments | `./core` | ⏳ Pending | Container cho dev/test environments |
| 11 | Deploy Naming Convention Policy | - Kiểm tra policy definition tồn tại<br>- Test policy với sample resource names | `./core/lib/policy_definitions` | ⏳ Pending | Enforce naming standards |
| 12 | Deploy Custom Policies | - Kiểm tra tất cả policy definitions<br>- Verify policy parameters | `./core/lib/policy_definitions` | ⏳ Pending | Additional governance policies |
| 13 | Assign Policies to Management Groups | - Kiểm tra policy assignments<br>- Test policy enforcement | `./core` | ⏳ Pending | Apply governance rules |
| **PHASE 2: MANAGEMENT RESOURCES** |
| 14 | Tạo Management Resource Group | - Verify RG name: `{root_id}-rg-management-{location}`<br>- Kiểm tra tags compliance | `./management` | ⏳ Pending | Container cho management resources |
| 15 | Deploy Infrastructure Log Analytics Workspace | - Verify LAW name: `{root_id}-law-infrastructure-{location}`<br>- Kiểm tra retention: 90 days<br>- Test log ingestion | `./management` | ⏳ Pending | Infrastructure monitoring workspace |
| 16 | Deploy Security Log Analytics Workspace | - Verify LAW name: `{root_id}-law-security-{location}`<br>- Kiểm tra retention: 180 days<br>- Test security log ingestion | `./management` | ⏳ Pending | Security monitoring workspace |
| 17 | Configure Infrastructure LAW Solutions | - Deploy VM Insights solution<br>- Deploy Change Tracking solution<br>- Deploy Updates solution<br>- Deploy Agent Health solution | `./management` | ⏳ Pending | Infrastructure monitoring solutions |
| 18 | Configure Security LAW Solutions | - Deploy Security solution<br>- Deploy SecurityInsights (if Sentinel enabled)<br>- Verify security data sources | `./management` | ⏳ Pending | Security monitoring solutions |
| 19 | Enable Microsoft Sentinel | - Verify Sentinel onboarding on Security LAW<br>- Test security analytics<br>- Configure data connectors | `./management` | ⏳ Pending | SIEM/SOAR platform |
| 20 | Deploy VM Insights Data Collection Rule | - Verify DCR for Infrastructure LAW<br>- Test performance counter collection<br>- Verify VM monitoring | `./management` | ⏳ Pending | VM performance monitoring DCR |
| **PHASE 3: CONNECTIVITY HUB** |
| 21 | Tạo Connectivity Resource Group | - Verify RG name: `{root_id}-connectivity-{location}`<br>- Kiểm tra location và tags | `./connectivity` | ⏳ Pending | Container cho networking resources |
| 22 | Deploy Hub Virtual Network | - Verify VNet name và address space: `**********/20`<br>- Kiểm tra location: southeastasia | `./connectivity` | ⏳ Pending | Central hub network |
| 23 | Tạo GatewaySubnet | - Verify subnet: `**********/27`<br>- Kiểm tra subnet configuration | `./connectivity` | ⏳ Pending | Cho VPN/ExpressRoute Gateway |
| 24 | Tạo AzureFirewallSubnet | - Verify subnet tự động tạo<br>- Kiểm tra subnet size đủ cho Firewall | `./connectivity` | ⏳ Pending | Cho Azure Firewall |
| 25 | Tạo AzureFirewallSubnet-Reverse | - Verify subnet: `***********/26`<br>- Kiểm tra routing configuration | `./connectivity` | ⏳ Pending | Additional firewall subnet |
| 26 | Tạo AzureBastionSubnet | - Verify subnet: `************/26`<br>- Kiểm tra subnet requirements | `./connectivity` | ⏳ Pending | Cho Azure Bastion |
| 27 | Tạo Firewall Public IP | - Verify public IP allocation<br>- Kiểm tra SKU và availability zone | `./connectivity` | ⏳ Pending | Public IP cho Azure Firewall |
| 28 | Tạo Bastion Public IP | - Verify public IP allocation<br>- Kiểm tra static assignment | `./connectivity` | ⏳ Pending | Public IP cho Azure Bastion |
| 29 | Deploy Azure Firewall | - Verify firewall deployment<br>- Test firewall rules<br>- Kiểm tra threat intelligence | `./connectivity` | ⏳ Pending | Central network security |
| 30 | Configure Firewall Policy | - Verify policy rules<br>- Test traffic filtering<br>- Kiểm tra logging | `./connectivity` | ⏳ Pending | Firewall rule management |
| 31 | Deploy Azure Bastion | - Verify Bastion deployment<br>- Test RDP/SSH connectivity<br>- Kiểm tra security features | `./connectivity` | ⏳ Pending | Secure remote access |
| 32 | Deploy VPN Gateway (Optional) | - Verify gateway deployment<br>- Test VPN connectivity<br>- Kiểm tra routing | `./connectivity` | ⏳ Pending | Site-to-site connectivity |
| 33 | Tạo Management VNet | - Verify VNet: `***********/21`<br>- Kiểm tra subnet configuration | `./connectivity` | ⏳ Pending | Management workload network |
| 34 | Configure Hub-Management Peering | - Verify peering status: Connected<br>- Test connectivity<br>- Kiểm tra routing | `./connectivity` | ⏳ Pending | Connect hub và management |
| **PHASE 4A: PRODUCTION SPOKE NETWORKS** |
| 35 | Tạo Product A Resource Group | - Verify RG name: `{root_id}-rg-product-a-{location}`<br>- Kiểm tra tags compliance | `./spoke` | ⏳ Pending | Container cho Product A resources |
| 36 | Deploy Product A VNet | - Verify VNet: `**********/23`<br>- Kiểm tra address space allocation | `./spoke` | ⏳ Pending | Product A network |
| 37 | Tạo Product A Shared Subnet | - Verify subnet: `**********/23`<br>- Kiểm tra usable IPs: 507 hosts | `./spoke` | ⏳ Pending | Shared resources subnet |
| 38 | Configure Product A NSG | - Verify NSG rules<br>- Test security rules<br>- Kiểm tra logging | `./spoke` | ⏳ Pending | Network security |
| 39 | Configure Product A Route Table | - Verify routing rules<br>- Test traffic routing<br>- Kiểm tra next hop | `./spoke` | ⏳ Pending | Traffic routing control |
| 40 | Configure Product A-Hub Peering | - Verify peering status: Connected<br>- Test connectivity to hub<br>- Kiểm tra gateway transit | `./spoke` | ⏳ Pending | Connect to hub network |
| 41 | Tạo Product Microservice Resource Group | - Verify RG name: `{root_id}-rg-product-microservice-{location}`<br>- Kiểm tra tags compliance | `./spoke` | ⏳ Pending | Container cho Microservice resources |
| 42 | Deploy Product Microservice VNet | - Verify VNet: `**********/22`<br>- Kiểm tra address space allocation | `./spoke` | ⏳ Pending | Microservice network |
| 43 | Tạo Microservice Shared Subnet | - Verify subnet: `**********/22`<br>- Kiểm tra usable IPs: 1019 hosts | `./spoke` | ⏳ Pending | Microservice subnet |
| 44 | Configure Microservice NSG | - Verify NSG rules<br>- Test security rules<br>- Kiểm tra application security | `./spoke` | ⏳ Pending | Microservice security |
| 45 | Configure Microservice Route Table | - Verify routing rules<br>- Test traffic routing<br>- Kiểm tra load balancing | `./spoke` | ⏳ Pending | Microservice routing |
| 46 | Configure Microservice-Hub Peering | - Verify peering status: Connected<br>- Test connectivity<br>- Kiểm tra bandwidth | `./spoke` | ⏳ Pending | Connect microservice to hub |
| 47 | Tạo Product C Resource Group | - Verify RG name: `{root_id}-rg-product-c-{location}`<br>- Kiểm tra tags compliance | `./spoke` | ⏳ Pending | Container cho Product C resources |
| 48 | Deploy Product C VNet | - Verify VNet: `**********/22`<br>- Kiểm tra address space allocation | `./spoke` | ⏳ Pending | Product C network |
| 49 | Tạo Product C Shared Subnet | - Verify subnet: `**********/22`<br>- Kiểm tra usable IPs: 1019 hosts | `./spoke` | ⏳ Pending | Product C subnet |
| 50 | Configure Product C NSG | - Verify NSG rules<br>- Test security rules<br>- Kiểm tra compliance | `./spoke` | ⏳ Pending | Product C security |
| 51 | Configure Product C Route Table | - Verify routing rules<br>- Test traffic routing<br>- Kiểm tra performance | `./spoke` | ⏳ Pending | Product C routing |
| 52 | Configure Product C-Hub Peering | - Verify peering status: Connected<br>- Test connectivity<br>- Kiểm tra latency | `./spoke` | ⏳ Pending | Connect Product C to hub |
| **PHASE 4B: NON-PRODUCTION SPOKE NETWORKS** |
| 53 | Tạo UAT Resource Group | - Verify RG name: `{root_id}-rg-uat-{location}`<br>- Kiểm tra tags compliance | `./spoke` | ⏳ Pending | Container cho UAT resources |
| 54 | Deploy UAT VNet | - Verify VNet: `**********/23`<br>- Kiểm tra address space allocation | `./spoke` | ⏳ Pending | UAT environment network |
| 55 | Tạo UAT Shared Subnet | - Verify subnet: `**********/23`<br>- Kiểm tra usable IPs: 507 hosts | `./spoke` | ⏳ Pending | UAT testing subnet |
| 56 | Configure UAT NSG | - Verify NSG rules<br>- Test security rules<br>- Kiểm tra test environment security | `./spoke` | ⏳ Pending | UAT security |
| 57 | Configure UAT Route Table | - Verify routing rules<br>- Test traffic routing<br>- Kiểm tra test connectivity | `./spoke` | ⏳ Pending | UAT routing |
| 58 | Configure UAT-Hub Peering | - Verify peering status: Connected<br>- Test connectivity<br>- Kiểm tra test traffic | `./spoke` | ⏳ Pending | Connect UAT to hub |
| 59 | Tạo DevTest A Resource Group | - Verify RG name: `{root_id}-rg-devtest-a-{location}`<br>- Kiểm tra tags compliance | `./spoke` | ⏳ Pending | Container cho DevTest resources |
| 60 | Deploy DevTest A VNet | - Verify VNet: `**********/23`<br>- Kiểm tra address space allocation | `./spoke` | ⏳ Pending | Development network |
| 61 | Tạo DevTest A Shared Subnet | - Verify subnet: `**********/23`<br>- Kiểm tra usable IPs: 507 hosts | `./spoke` | ⏳ Pending | Development subnet |
| 62 | Configure DevTest A NSG | - Verify NSG rules<br>- Test security rules<br>- Kiểm tra dev environment security | `./spoke` | ⏳ Pending | DevTest security |
| 63 | Configure DevTest A Route Table | - Verify routing rules<br>- Test traffic routing<br>- Kiểm tra dev connectivity | `./spoke` | ⏳ Pending | DevTest routing |
| 64 | Configure DevTest A-Hub Peering | - Verify peering status: Connected<br>- Test connectivity<br>- Kiểm tra development traffic | `./spoke` | ⏳ Pending | Connect DevTest to hub |

## 🔍 Post-Deployment Verification

| STT | Kiểm tra tổng thể | Các bước kiểm tra/ kiểm thử | Command/Tool | Trạng thái | Note |
|-----|------------------|---------------------------|-------------|-----------|------|
| 65 | Verify Management Groups Structure | - Check MG hierarchy trong Azure Portal<br>- Verify subscription assignments<br>- Test policy inheritance | Azure Portal > Management Groups | ⏳ Pending | Foundation verification |
| 66 | Test Policy Enforcement | - Tạo test resource với wrong naming<br>- Verify policy denial<br>- Check compliance reports | Azure Portal > Policy | ⏳ Pending | Governance verification |
| 67 | Verify Network Connectivity | - Test ping từ spoke to hub<br>- Test internet connectivity through firewall<br>- Verify DNS resolution | `ping`, `nslookup`, `tracert` | ⏳ Pending | Network verification |
| 68 | Test Azure Firewall Rules | - Test allowed traffic<br>- Test blocked traffic<br>- Verify logging | Azure Portal > Firewall Logs | ⏳ Pending | Security verification |
| 69 | Test Azure Bastion Connectivity | - Connect to VM via Bastion<br>- Test RDP/SSH functionality<br>- Verify security features | Azure Portal > Bastion | ⏳ Pending | Remote access verification |
| 70 | Verify Monitoring và Logging | - Check Log Analytics data ingestion<br>- Verify Security Center recommendations<br>- Test alerting rules | Azure Portal > Monitor | ⏳ Pending | Monitoring verification |
| 71 | Test Cross-Spoke Connectivity | - Test connectivity between spokes<br>- Verify traffic routing through hub<br>- Check firewall logs | Network testing tools | ⏳ Pending | Inter-spoke communication |
| 72 | Verify Resource Tagging | - Check all resources have required tags<br>- Verify tag compliance<br>- Test tag-based policies | Azure Resource Graph | ⏳ Pending | Compliance verification |

## 📊 Deployment Summary

### Resource Count by Module:
- **Core Module**: ~15 resources (Management Groups + Policies)
- **Management Module**: ~8 resources (Log Analytics + Security)
- **Connectivity Module**: ~12 resources (Hub Network + Firewall + Bastion)
- **Spoke Module**: ~30 resources (5 spoke networks × 6 resources each)
- **Total**: ~65 Azure resources

### Estimated Deployment Time:
- **Phase 1 (Core)**: 5-10 minutes
- **Phase 2 (Management)**: 10-15 minutes
- **Phase 3 (Connectivity)**: 15-25 minutes
- **Phase 4 (Spokes)**: 10-15 minutes (parallel)
- **Total**: 40-65 minutes

### Critical Success Factors:
1. ✅ All Management Groups created successfully
2. ✅ Policy assignments applied without errors
3. ✅ Hub network connectivity established
4. ✅ All VNet peerings in "Connected" state
5. ✅ Azure Firewall rules configured correctly
6. ✅ Monitoring và logging operational
7. ✅ All resources tagged according to policy

## 🚨 Troubleshooting Common Issues

| Issue | Possible Cause | Solution |
|-------|---------------|----------|
| Policy assignment fails | Insufficient permissions | Verify Owner role on Management Group |
| VNet peering fails | Address space overlap | Check IP address planning |
| Firewall deployment fails | Subnet size too small | Verify AzureFirewallSubnet ≥ /26 |
| Bastion deployment fails | Incorrect subnet name | Verify subnet name = "AzureBastionSubnet" |
| Spoke connectivity fails | NSG blocking traffic | Check NSG rules và firewall rules |

## 📝 Status Legend
- ⏳ **Pending**: Chưa bắt đầu
- 🔄 **In Progress**: Đang thực hiện
- ✅ **Completed**: Hoàn thành
- ❌ **Failed**: Thất bại
- ⚠️ **Warning**: Có cảnh báo
